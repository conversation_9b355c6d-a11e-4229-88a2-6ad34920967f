import { CosmosClient } from "@azure/cosmos";
import { CustomLogger } from "./log";
import { decrypt } from '../utilities/encryption';
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

const MAIL_DELETE_START_DATE = process.env.MAIL_DELETE_START_DATE ?? '2025-01-01';
const MAIL_DELETE_END_DATE = process.env.MAIL_DELETE_END_DATE ?? '2025-01-31';
const MAIL_MAX_FETCH_BATCH_SIZE = parseInt(process.env.MAIL_MAX_FETCH_BATCH_SIZE ?? '100');
const MAIL_MAX_DISPLAY_SIZE = parseInt(process.env.MAIL_MAX_DISPLAY_SIZE ?? '0');

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function deleteTargetDataFromCosmos1(logger: any) {
  const startDate = MAIL_DELETE_START_DATE + "T00:00:00.00Z";
  const endDate = MAIL_DELETE_END_DATE + "T23:59:59.99Z";

  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Fetching Mails...`);

  if (!databaseName || !containerName) {
    throw new Error("[CosmosDB:deleteTargetDataFromCosmos] databaseName and containerName must be defined");
  }

  const countQuery = {
    query: `
      SELECT VALUE COUNT(1) FROM c 
      WHERE c.receivedDateTime >= @startDate 
      AND c.receivedDateTime <= @endDate
    `,
    parameters: [
      { name: "@startDate", value: startDate },
      { name: "@endDate", value: endDate }
    ]
  };

  const client = getClient(logger);
  const database = client.database(databaseName);
  const container = database.container(containerName);
  
  const { resources: countResult } = await container.items.query(countQuery).fetchAll();
  const totalMailsInDateRange = countResult[0] ?? 0;

  let mailsProcessedSoFar = 0;
  let currentBatchNumber = 0;
  let allDecryptedMails: any[] = [];
  const mailBatchSize = MAIL_MAX_FETCH_BATCH_SIZE;
  const mailDisplaySize = MAIL_MAX_DISPLAY_SIZE;
  const totalBatches = Math.ceil(totalMailsInDateRange / mailBatchSize);

  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Total ${totalMailsInDateRange} Items in Date Range ${MAIL_DELETE_START_DATE} to ${MAIL_DELETE_END_DATE}`);
  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Total Split Batch: ${totalBatches} | MAX_BATCH_COUNTS: ${mailBatchSize}`);

  const querySpec = {
    query: `
      SELECT *
      FROM c 
      WHERE c.receivedDateTime >= @startDate 
      AND c.receivedDateTime <= @endDate
      ORDER BY c.receivedDateTime ASC
    `,
    parameters: [
      { name: "@startDate", value: startDate },
      { name: "@endDate", value: endDate }
    ]
  };

  // Use async iterator approach
  const queryIterator = container.items.query(querySpec, {
    maxItemCount: mailBatchSize
  });

  // Process using async iterator
  for await (const response of queryIterator.getAsyncIterator()) {
    currentBatchNumber++;
    logger.log(`---- START BATCH | ${currentBatchNumber} ----`);

    const currentBatch = response.resources;
    const mailsInCurrentBatch = currentBatch.length;
    
    if (mailsInCurrentBatch === 0) {
      logger.log(`[CosmosDB:deleteTargetDataFromCosmos] No mails in this batch, continuing...`);
      continue;
    }

    mailsProcessedSoFar += mailsInCurrentBatch;

    // Process each mail in the batch
    let mailsDecryptedInThisBatch = 0;
    for (const mail of currentBatch) {
      try {
        const decryptedMail = {
          id: mail.id,
          subject: decrypt(mail.subject),
          from: {
            emailAddress: {
              name: decrypt(mail.from.emailAddress.name),
              address: decrypt(mail.from.emailAddress.address)
            }
          },
          receivedDateTime: mail.receivedDateTime,
          bodyPreview: decrypt(mail.bodyPreview)
        };
        
        allDecryptedMails.push(decryptedMail);
        mailsDecryptedInThisBatch++;
        
      } catch (decryptError) {
        logger.error(`[deleteTargetDataFromCosmos] Decrypt Failed for Mail ${mail.id}: ${decryptError}`);
      }
    }

    logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Processed ${mailsDecryptedInThisBatch}/${mailsInCurrentBatch} Mails | Total: ${mailsProcessedSoFar} of ${totalMailsInDateRange}`);
    logger.log(`---- END BATCH | ${currentBatchNumber} ----`);
  }












  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Processing Complete - ${currentBatchNumber} Total Batches Processed`);
  logger.log(`-+-+-+ END - Fetch and Decrypt Mail -+-+-+`);

  logger.log(`\n-+-+-+ START - Count Target Mails -+-+-+`);
  const targetMails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  const mailCounts = new Map();
  targetMails.forEach(mail => mailCounts.set(mail, 0));

  // Count mails from target addresses
  allDecryptedMails.forEach(mail => {
    const address = mail.from.emailAddress.address;
    if (mailCounts.has(address)) {
      mailCounts.set(address, mailCounts.get(address) + 1);
    }
  });

  targetMails.forEach(mail => {
    const count = mailCounts.get(mail);
    logger.log(`${mail}: ${count} mails`);
  });

  const totalTargetMails = Array.from(mailCounts.values()).reduce((sum, count) => sum + count, 0);
  logger.log(`------------------------------------------------`);
  logger.log(`Total Target Mails: ${totalTargetMails}`);
  logger.log(`------------------------------------------------`);
  logger.log(`-+-+-+ END - Count Target Mails -+-+-+\n`);

  const summary = { 
    totalMailsFound: totalMailsInDateRange,
    totalDecrypted: allDecryptedMails.length,
    targetMailCounts: Object.fromEntries(mailCounts),
    totalTargetMails: totalTargetMails,
    dateRange: { startDate, endDate },
    mailsInJsonDisplay: Math.min(allDecryptedMails.length, mailDisplaySize),
    mails: allDecryptedMails.slice(0, mailDisplaySize)
  };
  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] summary:`, JSON.stringify(summary, null, 2));


}


export async function deleteTargetDataFromCosmos(logger: any) {
  const startDate = MAIL_DELETE_START_DATE + "T00:00:00.00Z";
  const endDate = MAIL_DELETE_END_DATE + "T23:59:59.99Z";

  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Fetching Mails...`);

  if (!databaseName || !containerName) {
    throw new Error("[CosmosDB:deleteTargetDataFromCosmos] databaseName and containerName must be defined");
  }

  const countQuery = {
    query: `
      SELECT VALUE COUNT(1) FROM c 
      WHERE c.receivedDateTime >= @startDate 
      AND c.receivedDateTime <= @endDate
    `,
    parameters: [
      { name: "@startDate", value: startDate },
      { name: "@endDate", value: endDate }
    ]
  };

  const client = getClient(logger);
  const database = client.database(databaseName);
  const container = database.container(containerName);
  
  const { resources: countResult } = await container.items.query(countQuery).fetchAll();
  const totalMailsInDateRange = countResult[0] ?? 0;

  let mailsProcessedSoFar = 0;
  let currentBatchNumber = 0;
  let totalDecrypted = 0;
  let totalDeleted = 0;
  
  let displayMails: any[] = [];
  const mailBatchSize = MAIL_MAX_FETCH_BATCH_SIZE;
  const mailDisplaySize = MAIL_MAX_DISPLAY_SIZE;
  const totalBatches = Math.ceil(totalMailsInDateRange / mailBatchSize);

  // Target emails to delete
  const targetEmails = JSON.parse(process.env.MAIL_DELETE_TARGET_MAILS || '[]');

  // Initialize mail counts
  const mailCounts = new Map();
  targetEmails.forEach((email: string) => mailCounts.set(email, 0));

  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Total ${totalMailsInDateRange} Items in Date Range ${startDate} to ${endDate}`);
  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Total Split Batch: ${totalBatches} | MAX_BATCH_COUNTS: ${mailBatchSize}`);
  logger.log(`\n**** START | PROCESSSING MAIL ****`);
  const querySpec = {
    query: `
      SELECT *
      FROM c 
      WHERE c.receivedDateTime >= @startDate 
      AND c.receivedDateTime <= @endDate
      ORDER BY c.receivedDateTime ASC
    `,
    parameters: [
      { name: "@startDate", value: startDate },
      { name: "@endDate", value: endDate }
    ]
  };

  // Use async iterator approach
  const queryIterator = container.items.query(querySpec, {
    maxItemCount: mailBatchSize
  });

  // Process using async iterator
  for await (const response of queryIterator.getAsyncIterator()) {
    currentBatchNumber++;
    logger.log(`++++ START BATCH | FETCH, DECRYPT, DELETE MAIL / ${currentBatchNumber} of ${totalBatches} ++++`);

    const currentBatch = response.resources;
    const mailsInCurrentBatch = currentBatch.length;
    
    if (mailsInCurrentBatch === 0) {
      logger.log(`[CosmosDB:deleteTargetDataFromCosmos] No mails in this batch, continuing...`);
      continue;
    }

    mailsProcessedSoFar += mailsInCurrentBatch;

    // Process each mail in the batch
    let mailsDecryptedInThisBatch = 0;
    let mailsDeletedInThisBatch = 0;
    
    for (const mail of currentBatch) {
      try {
        const decryptedMail = {
          id: mail.id,
          subject: decrypt(mail.subject),
          from: {
            emailAddress: {
              name: decrypt(mail.from.emailAddress.name),
              address: decrypt(mail.from.emailAddress.address)
            }
          },
          receivedDateTime: mail.receivedDateTime,
          bodyPreview: decrypt(mail.bodyPreview)
        };
        
        totalDecrypted++;
        mailsDecryptedInThisBatch++;
        
        const emailAddress = decryptedMail.from.emailAddress.address;
        
        if (targetEmails.includes(emailAddress)) {
          // Count the target email
          mailCounts.set(emailAddress, mailCounts.get(emailAddress) + 1);
          
          // Delete the email from Cosmos DB
          await container.item(mail.id, mail.id).delete();
          totalDeleted++;
          mailsDeletedInThisBatch++;
          
          logger.log(`Deleted target email: ${emailAddress} | Subject: ${decryptedMail.subject}`);
        }
        // Display mails that are not in target emails
        if (!targetEmails.includes(emailAddress) && displayMails.length < mailDisplaySize) {
          displayMails.push(decryptedMail);
        }
        
      } catch (error) {
        logger.error(`[deleteTargetDataFromCosmos] Error processing mail ${mail.id}: ${error}`);
      }
    }
    
    logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Processed ${mailsDecryptedInThisBatch} of ${mailsInCurrentBatch} Mails | Deleted ${mailsDeletedInThisBatch} | Total Processed: ${mailsProcessedSoFar} of ${totalMailsInDateRange}`);
    logger.log(`++++ END BATCH | FETCH, DECRYPT, DELETE MAIL / ${currentBatchNumber} of ${totalBatches} ++++\n\n`);
  }
  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] Processing Complete - ${currentBatchNumber} Total Batches Processed`);
  logger.log(`**** END | PROCESSSING MAIL ****`);

  logger.log(`\n**** START | TARGET MAIL COUNT ****`);
  targetEmails.forEach((email: string) => {
    const count = mailCounts.get(email);
    logger.log(`> ${email}: ${count} Mails (Deleted)`);
  });
  const totalTargetMails = Array.from(mailCounts.values()).reduce((sum, count) => sum + count, 0);
  logger.log(`------------------------------------------------`);
  logger.log(`Total Target Mails Found: ${totalTargetMails}`);
  logger.log(`Total Successfully Deleted: ${totalDeleted}`);
  logger.log(`**** END | TARGET MAIL COUNT ****\n`);

  logger.log(`**** START | SUMMARY ****`);
  const summary = { 
    totalMailsFound: totalMailsInDateRange,
    totalDecrypted: totalDecrypted,
    totalTargetMailsFound: totalTargetMails,
    totalDeleted: totalDeleted,
    targetMailCounts: Object.fromEntries(mailCounts),
    dateRange: { startDate, endDate },
    mailsInJsonDisplay: displayMails.length,
    displayMails: displayMails
  };
  logger.log(`[CosmosDB:deleteTargetDataFromCosmos] summary:`, JSON.stringify(summary, null, 2));

  // Final count check
  const finalCountQuery = {
    query: `SELECT VALUE COUNT(1) FROM c`
  };
  const { resources: finalCountResult } = await container.items.query(finalCountQuery).fetchAll();
  const finalTotalEmails = finalCountResult[0] ?? 0;
  logger.log(`Total Mails Remaining in CosmosDB: ${finalTotalEmails}`);

  logger.log(`**** END | SUMMARY ****\n`);

}
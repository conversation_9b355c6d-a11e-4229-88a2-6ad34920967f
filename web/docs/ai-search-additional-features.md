# AI検索 追加機能 詳細仕様書

## 概要

AI検索機能には、フィルター機能、タブ切り替え機能、OpenAIController以外にも多くの重要な機能が実装されています。これらの機能は、ユーザーエクスペリエンス、パフォーマンス、監視、エラーハンドリングなどの観点で AI検索の品質を支える重要な役割を担っています。

## 1. SearchInput コンポーネント（チャット機能統合）

### 概要
AI検索モードでは、従来の検索入力フィールドに加えて、チャット形式のUI要素が統合されています。

### 主要機能
- **モード別プレースホルダー**: 検索モードに応じた適切なガイダンス表示
- **チャット履歴表示**: ユーザーとAIの対話履歴を表示
- **返信機能**: AI検索結果に対するレスポンス表示

### 実装詳細

#### プレースホルダーテキスト
```typescript
export const PlaceholderLabel = {
  BLANK: 'キーワードを入力',
  FOCUS: 'タイトル、分類、本文を検索',
};

export const PlaceholderLabelForAI = {
  BLANK: '自然言語で検索できます',
  FOCUS: '',
};

const placeholder = React.useMemo(() => {
  if (searchMode === SearchListMode.DEFAULT) {
    return view === SearchInputView.NOT_FOCUS_NO_VALUE
      ? PlaceholderLabel.BLANK
      : PlaceholderLabel.FOCUS;
  }
  return view === SearchInputView.NOT_FOCUS_NO_VALUE
    ? PlaceholderLabelForAI.BLANK
    : PlaceholderLabelForAI.FOCUS;
}, [view, searchMode]);
```

#### チャット機能
```tsx
{searchMode === SearchListMode.Chat && (
  <Chat className="chat-container">
    <ChatItem attached>
      {messages.map((msg) => (
        msg.sender === 'Me' ? (
          <div className="ChatMyMessage">
            <ChatMessage mine>{msg.text}</ChatMessage>
          </div>
        ) : (
          <div className="ChatMessage">
            <ChatMessage>{msg.text}</ChatMessage>
          </div>
        )
      ))}
    </ChatItem>
    <div ref={latestMessageRef} />
  </Chat>
)}
```

#### メッセージ型定義
```typescript
type Message = {
  sender: 'User' | 'atTane';
  text: string;
};
```

## 2. AI検索結果変換機能

### 概要
OpenAIControllerから返される検索結果を、フロントエンドで表示可能な形式に変換する機能です。

### データソース別変換

#### Mail検索結果変換
```typescript
export function convertMail(result: any): ISplitViewListSingle {
  const { document } = result;
  return {
    id: document.id ?? '',
    kind: 'Mail',
    score: result.semanticSearch?.rerankerScore,
    title: document.subject ?? 'No Title',
    note: document.from?.emailAddress?.name ?? document.from?.emailAddress?.address,
    displayDate: document.receivedDateTime ?? '表示日時',
    properties: {
      hasAttachments: document.hasAttachments,
    },
  };
}
```

#### SPO検索結果変換
```typescript
export function convertSPO(result: any): ISplitViewListSingle {
  return {
    id: result.document.id ?? '',
    kind: 'SPO',
    score: result.semanticSearch?.rerankerScore,
    title: result.document.properties.title ?? 'No Title',
    note: result.document.properties.category1 ?? 'No Category',
    displayDate: result.document.properties.updatedDate ?? '表示日時',
    properties: {
      hasAttachments: result.document.properties.hasAttachments,
      siteUrl: result.document.properties.siteUrl,
      listUrl: result.document.properties.listUrl,
      editLink: result.document.properties.editLink ?? '',
      listName: result.document.properties.listName ?? 'No Category',
    },
  };
}
```

#### 統合変換機能
```typescript
const convertResultToSplitViewListSingle = React.useCallback(
  (results: any[]): ISplitViewListSingle[] => results.map((result) => {
    const kind = result.document?.kind;
    if (kind === 'Mail') return convertMail(result);
    if (kind === 'Chat') return convertChat(result);
    if (kind === 'SPO') return convertSPO(result);
    return convertUnknown(result);
  }),
  [],
);
```

## 3. AI検索ログ機能

### 概要
AI検索の実行状況、結果、エラーを詳細に記録し、分析・改善に活用するためのログ機能です。

### ログ種別

#### AI検索実行ログ
```typescript
export function sendAISearchLog(
  inputValue: string,
  dateFilter: string,
  sourceFilter: string,
  resultCount: number,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'EXECUTE_AI_SEARCH',
    customProperties: {
      searchQuery: inputValue,
      dateFilter: dateFilter || 'none',
      sourceFilter: sourceFilter || 'none',
      resultCount,
      executionDate: new Date().toISOString(),
    },
  });
}
```

#### 検索結果なしログ
```typescript
// 検索結果が0件の場合
reportEvent({
  type: EventReportType.SYS_EVENT,
  name: 'NO_AI_SEARCH_RESULT',
});
```

#### エラーログ
```typescript
.catch((error: Error) => {
  reportEvent({
    type: EventReportType.SYS_ERROR,
    name: 'SEARCH_ERROR',
    error,
  });
});
```

### ログ項目
- **検索クエリ**: ユーザーが入力した検索文字列
- **フィルター情報**: 日付フィルター、ソースフィルターの設定
- **検索結果件数**: 取得された結果の件数
- **実行日時**: 検索実行のタイムスタンプ
- **エラー情報**: 発生したエラーの詳細

## 4. AI検索API呼び出し機能

### 概要
フロントエンドからOpenAIControllerへのAPI呼び出しを管理する機能です。

### 実装詳細

#### API呼び出し関数
```typescript
const fetchResultFromAIImpl = async (
  input: string,
  userId?: string,
  groupIds?: string[],
  from?: string,
  to?: string,
  sourceFilter?: string,
) => {
  try {
    const response = await fetch(`${PREFIX}/openai/aisearch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        Input: input,
        UserId: userId,
        GroupIds: groupIds,
        From: from,
        To: to,
        SourceFilter: sourceFilter,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      list: data.MergedResult || [],
      reply: `${data.MergedResult?.length || 0}件の検索結果が見つかりました。`,
    };
  } catch (error) {
    console.error('AI search API error:', error);
    throw error;
  }
};
```

#### カスタムフック
```typescript
const useOpenAIApiAccessor = () => {
  const fetchResultFromAI = React.useCallback(
    (searchInputValue: string,
      userId: string,
      groupIds?: string[],
      from?: string,
      to?: string,
      sourceFilter?: string) => fetchResultFromAIImpl(
      searchInputValue,
      userId,
      groupIds,
      from,
      to,
      sourceFilter,
    ),
    [],
  );
  return { fetchResultFromAI };
};
```

## 5. AI検索実行フロー制御

### 概要
AI検索の実行から結果表示までの複雑なフローを制御する機能です。

### 実行フロー

#### メイン実行関数
```typescript
export async function onSubmitChatSearchImpl(
  inputValue: string,
  oid: string,
  fetchResultFromAI: Function,
  fetchGroupId: FetchGroupIds | undefined,
  setListMode: (mode: ListModeType) => void,
  setInputValue: (v: string) => void,
  from: string,
  to: string,
  setDateFilter: (dateFilter: string) => void,
  sourceFilterRef: string,
  setSourceFilter: (sourceFilter: string) => void,
  setChatLastSubmittedWords: (v: string) => void,
  setChatListDataRef: (list: ISplitViewListSingle[]) => void,
  dispatch: SplitViewDispatch,
  setReply: (s: string) => void,
  convertResultToSplitViewListSingle: Function,
  reportEvent: EventReporter,
): Promise<void>
```

#### 処理ステップ
1. **前処理**: 検索モード切り替え、入力値クリア、リストデータ初期化
2. **グループID取得**: ユーザーのグループ情報を取得
3. **状態初期化**: 詳細表示をクリア、コンテキストをリセット
4. **検索実行**: OpenAIControllerへのAPI呼び出し
5. **結果処理**: 検索結果の変換と状態更新
6. **ログ送信**: 実行結果のログ記録

#### エラーハンドリング
```typescript
.catch((error: Error) => {
  reportEvent({
    type: EventReportType.SYS_ERROR,
    name: 'SEARCH_ERROR',
    error,
  });
  dispatch({
    type: 'SET_ERROR',
    payload: SplitViewListMessage.API_REQUEST_FAIL,
  });
});
```

## 6. AI検索結果表示機能

### 概要
AI検索の結果を適切な形式でユーザーに表示する機能です。

### 表示要素

#### 検索結果リスト項目
- **タイトル**: ドキュメントのタイトル
- **ノート**: 送信者情報やカテゴリ
- **表示日時**: 受信日時や更新日時
- **データソースアイコン**: Mail/SPO/Teamsの識別
- **スコア**: セマンティック検索のRerankerScore
- **添付ファイル有無**: hasAttachmentsフラグ

#### チャットモード特有の表示
```typescript
chatMode?: boolean;  // チャットモード表示フラグ
```

### スタイリング
- **データソースアイコン**: 各データソースに応じたアイコン表示
- **スコア表示**: AI検索時のみ関連度スコアを表示
- **レスポンシブ対応**: モバイルデバイスでの最適化

## 7. AI検索キャッシュ機能

### 概要
AI検索の結果をローカルストレージにキャッシュし、パフォーマンスを向上させる機能です。

### キャッシュ構造

#### キャッシュステータス
```typescript
export const CacheStatus = {
  REQUEST_IN_PROGRESS: 'RequestInProgress',
  SEARCH_ON_INTERVAL: 'SearchOnInterval',
  COMPLETED: 'Completed',
  ERROR: 'Error',
};
```

#### 検索要求キャッシュ
```typescript
export interface ISearchRequestCache {
  status?: CacheStatusType,
  result?: ISplitViewListSingle[],
  timestamp?: Date,
  query?: string,
  filters?: {
    dateFilter?: string,
    sourceFilter?: string,
  },
}
```

### キャッシュ操作
- **保存**: 検索結果の永続化
- **取得**: キャッシュされた結果の読み込み
- **更新**: 既存キャッシュの更新
- **削除**: 期限切れキャッシュの削除

## 8. パフォーマンス監視機能

### 概要
AI検索のパフォーマンスを監視し、ボトルネックを特定するための機能です。

### 監視項目

#### 実行時間測定
```typescript
const startTime = DateTime.UtcNow;
// 検索処理実行
const duration = DateTime.UtcNow - startTime;
_logger.LogInformation("Search request completed in {Duration}ms", duration.TotalMilliseconds);
```

#### メトリクス収集
- **検索実行時間**: API呼び出しから結果取得までの時間
- **結果変換時間**: データ変換処理の時間
- **UI更新時間**: 結果表示までの時間
- **メモリ使用量**: 検索処理中のメモリ消費

### パフォーマンス最適化
- **並列処理**: Mail検索とSPO検索の同時実行
- **結果制限**: 最大50件での制限
- **メモ化**: 計算結果のキャッシュ
- **遅延読み込み**: 必要に応じた段階的データ取得

## テスト要件策定のためのチェックポイント

### 1. SearchInput コンポーネント（チャット機能）のテスト項目

#### 基本動作テスト
- [ ] 検索モードに応じてプレースホルダーが正しく表示される
- [ ] AIモード時にチャット履歴が表示される
- [ ] メッセージの送信者（Me/CPU）に応じて適切なスタイルが適用される
- [ ] 最新メッセージへの自動スクロールが動作する
- [ ] フォーカス状態に応じてプレースホルダーが変化する

#### チャット機能テスト
- [ ] ChatMessageコンポーネントが正しくレンダリングされる
- [ ] mine属性によってユーザーメッセージのスタイルが適用される
- [ ] メッセージ配列の変更時に適切に再レンダリングされる
- [ ] latestMessageRefが正しく設定される
- [ ] チャット履歴の表示順序が正しい

#### UI状態テスト
- [ ] SearchInputViewの状態変化が正しく動作する
- [ ] クラス名が状態に応じて正しく設定される
- [ ] フォーム送信時の動作が正常
- [ ] disabled状態の制御が適切

### 2. AI検索結果変換機能のテスト項目

#### データソース別変換テスト
- [ ] Mail検索結果が正しくISplitViewListSingle形式に変換される
- [ ] SPO検索結果が正しくISplitViewListSingle形式に変換される
- [ ] Chat検索結果が正しくISplitViewListSingle形式に変換される
- [ ] 不明なデータソースが適切に処理される
- [ ] 必須フィールドの欠損時にデフォルト値が設定される

#### フィールド変換テスト
- [ ] document.idが正しくidフィールドに設定される
- [ ] kindフィールドがデータソースに応じて設定される
- [ ] semanticSearch.rerankerScoreがscoreフィールドに設定される
- [ ] タイトルが適切に設定される（デフォルト値含む）
- [ ] 表示日時が正しく変換される
- [ ] プロパティオブジェクトが適切に構築される

#### エラーハンドリングテスト
- [ ] null/undefinedの検索結果が適切に処理される
- [ ] 不正な形式のデータが例外を発生させない
- [ ] 空配列の検索結果が正しく処理される
- [ ] 大量データの変換が正常に完了する

### 3. AI検索ログ機能のテスト項目

#### ログ送信テスト
- [ ] sendAISearchLogが正しいパラメータでログを送信する
- [ ] EventReportTypeが適切に設定される
- [ ] customPropertiesに必要な情報が含まれる
- [ ] 実行日時が正しく記録される
- [ ] フィルター情報が適切にログに含まれる

#### ログ内容テスト
- [ ] 検索クエリが正確に記録される
- [ ] dateFilterの有無が正しく判定される
- [ ] sourceFilterの有無が正しく判定される
- [ ] 検索結果件数が正確に記録される
- [ ] エラー情報が詳細に記録される

#### ログ種別テスト
- [ ] USER_EVENTログが適切に送信される
- [ ] SYS_EVENTログが適切に送信される
- [ ] SYS_ERRORログが適切に送信される
- [ ] ログレベルが正しく設定される

### 4. AI検索API呼び出し機能のテスト項目

#### API呼び出しテスト
- [ ] 正しいエンドポイントにリクエストが送信される
- [ ] リクエストヘッダーが適切に設定される
- [ ] リクエストボディが正しいJSON形式で送信される
- [ ] レスポンスが正しく解析される
- [ ] エラーレスポンスが適切に処理される

#### パラメータテスト
- [ ] 必須パラメータ（input, userId）が正しく送信される
- [ ] オプションパラメータ（groupIds, from, to, sourceFilter）が適切に処理される
- [ ] パラメータの型変換が正しく行われる
- [ ] null/undefinedパラメータが適切に処理される

#### エラーハンドリングテスト
- [ ] HTTPエラーステータスが適切に処理される
- [ ] ネットワークエラーが適切に処理される
- [ ] タイムアウトエラーが適切に処理される
- [ ] JSON解析エラーが適切に処理される
- [ ] エラーメッセージが適切に設定される

### 5. AI検索実行フロー制御のテスト項目

#### フロー制御テスト
- [ ] onSubmitChatSearchImplが正しい順序で処理を実行する
- [ ] 前処理（モード切り替え、初期化）が正常に動作する
- [ ] グループID取得が正常に完了する
- [ ] 状態初期化が適切に行われる
- [ ] 検索実行が正常に完了する
- [ ] 結果処理が適切に行われる
- [ ] ログ送信が正常に実行される

#### 状態管理テスト
- [ ] setListModeが正しく呼び出される
- [ ] setInputValueが適切に実行される
- [ ] dispatchアクションが正しく送信される
- [ ] フィルター状態がリセットされる
- [ ] 検索結果が正しく保存される

#### 非同期処理テスト
- [ ] Promise.allによる並列処理が正常動作する
- [ ] 非同期エラーが適切にキャッチされる
- [ ] タイムアウト処理が正常動作する
- [ ] 競合状態が適切に処理される

### 6. AI検索結果表示機能のテスト項目

#### 表示内容テスト
- [ ] 検索結果リスト項目が正しく表示される
- [ ] データソースアイコンが適切に表示される
- [ ] スコア表示がAI検索時のみ表示される
- [ ] 添付ファイル有無が正しく表示される
- [ ] 表示日時が適切にフォーマットされる

#### チャットモード表示テスト
- [ ] chatModeフラグに応じて表示が変化する
- [ ] チャットモード特有のスタイルが適用される
- [ ] 通常モードとの表示差異が正しく実装される

#### レスポンシブ対応テスト
- [ ] モバイルデバイスでの表示が適切
- [ ] 画面サイズ変更時のレイアウト調整が正常
- [ ] タッチ操作での使いやすさが確保される

### 7. AI検索キャッシュ機能のテスト項目

#### キャッシュ操作テスト
- [ ] 検索結果の保存が正常に動作する
- [ ] キャッシュされた結果の取得が正常に動作する
- [ ] キャッシュの更新が適切に行われる
- [ ] 期限切れキャッシュの削除が正常に動作する
- [ ] キャッシュサイズの制限が適切に機能する

#### キャッシュステータステスト
- [ ] REQUEST_IN_PROGRESSステータスが正しく設定される
- [ ] SEARCH_ON_INTERVALステータスが正しく設定される
- [ ] COMPLETEDステータスが正しく設定される
- [ ] ERRORステータスが正しく設定される
- [ ] ステータス遷移が適切に行われる

#### データ整合性テスト
- [ ] キャッシュデータの整合性が保たれる
- [ ] 並行アクセス時の競合が適切に処理される
- [ ] ブラウザリロード後のキャッシュ復元が正常
- [ ] ストレージ容量不足時の処理が適切

### 8. パフォーマンス監視機能のテスト項目

#### 実行時間測定テスト
- [ ] 検索実行時間が正確に測定される
- [ ] 結果変換時間が正確に測定される
- [ ] UI更新時間が正確に測定される
- [ ] 測定結果が適切にログ出力される

#### メトリクス収集テスト
- [ ] メモリ使用量の監視が正常動作する
- [ ] パフォーマンスカウンターが正しく記録される
- [ ] メトリクスデータの送信が正常動作する
- [ ] 異常値の検出が適切に行われる

#### 最適化効果テスト
- [ ] 並列処理による性能向上が確認できる
- [ ] 結果制限による性能改善が確認できる
- [ ] メモ化による最適化効果が確認できる
- [ ] 遅延読み込みによる改善効果が確認できる

### 9. 統合テスト項目

#### エンドツーエンドテスト
- [ ] 検索入力からAPI呼び出しまでの完全なフロー
- [ ] API応答から結果表示までの完全なフロー
- [ ] エラー発生から回復までの完全なフロー
- [ ] キャッシュ利用を含む完全なフロー

#### 他機能との連携テスト
- [ ] フィルター機能との連携が正常動作する
- [ ] タブ切り替え機能との連携が正常動作する
- [ ] ブックマーク機能との連携が正常動作する
- [ ] 詳細表示機能との連携が正常動作する

### 10. 回帰テスト項目

#### 機能回帰テスト
- [ ] 新機能追加が既存機能に影響しない
- [ ] パフォーマンス改善が機能を損なわない
- [ ] UI変更が操作性を悪化させない

#### データ互換性テスト
- [ ] 過去のキャッシュデータとの互換性
- [ ] APIバージョン変更への対応
- [ ] 設定変更時の動作確認

## テスト自動化の推奨事項

### 単体テスト（Jest + React Testing Library）
- コンポーネントの基本動作
- データ変換ロジック
- API呼び出し機能
- ログ送信機能

### 統合テスト（Cypress/Playwright）
- ユーザーフローの端到端テスト
- チャット機能の操作テスト
- 検索結果表示のテスト

### パフォーマンステスト
- 検索実行時間の測定
- メモリ使用量の監視
- 大量データでの性能テスト

### アクセシビリティテスト
- チャット機能のスクリーンリーダー対応
- キーボードナビゲーション
- 色覚対応

これらのテスト項目を体系的に実装することで、AI検索の追加機能の品質と信頼性を確保し、優れたユーザーエクスペリエンスを提供できます。

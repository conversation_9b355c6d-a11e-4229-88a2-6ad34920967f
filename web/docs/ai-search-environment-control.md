# AI検索機能の環境変数制御

## 概要

AI検索機能は環境変数 `REACT_APP_AI_SEARCH_ENABLED` によって有効/無効を制御できます。
この機能により、AI課金している会社のみAI検索を有効にし、その他の会社では従来の検索機能のみを提供することができます。

## 環境変数設定

### REACT_APP_AI_SEARCH_ENABLED

- **型**: boolean
- **デフォルト値**: true
- **説明**: AI検索機能の有効/無効を制御

#### 設定値

- `true`: AI検索機能を有効にする
  - AISearchタブが表示される
  - ユーザーはDefaultタブとAISearchタブを切り替えできる
  - AI検索API呼び出しが可能

- `false`: AI検索機能を無効にする
  - AISearchタブが非表示になる
  - 常にDefaultモードで動作
  - AI検索API呼び出しは実行されない

### 設定方法

#### 1. 環境変数ファイル (.env)

```bash
# AI検索を有効にする場合
REACT_APP_AI_SEARCH_ENABLED=true

# AI検索を無効にする場合
REACT_APP_AI_SEARCH_ENABLED=false
```

#### 2. ビルド時環境変数

```bash
# ビルド時に環境変数を設定
REACT_APP_AI_SEARCH_ENABLED=false npm run build
```

#### 3. Azure App Service設定

Azure App Serviceの場合、アプリケーション設定で環境変数を設定：

```
名前: REACT_APP_AI_SEARCH_ENABLED
値: true または false
```

## 実装詳細

### 影響を受けるコンポーネント

#### 1. SplitViewListTabs
- AI検索が無効の場合、コンポーネント全体が非表示になる
- `environment.REACT_APP_AI_SEARCH_ENABLED` をチェックして表示制御

#### 2. SplitViewList
- AI検索が無効の場合、タブ切り替え機能を無効化
- タブコンテナの条件付き表示

#### 3. useSplitViewReducers
- AI検索が無効の場合、searchModeの切り替えを制限
- Chatモードへの切り替えを防ぐ

### コード例

```typescript
// SplitViewListTabsコンポーネント
const SplitViewListTabs: React.FC<ISplitViewListTabsProps> = (props) => {
  const isAISearchEnabled = environment.REACT_APP_AI_SEARCH_ENABLED;

  // AI検索が無効の場合、タブ自体を表示しない
  if (!isAISearchEnabled) {
    return null;
  }

  // 通常のタブ表示処理...
};
```

```typescript
// SplitViewListコンポーネント
const SplitViewList: React.FC<ISplitViewListProps> = (props) => {
  const isAISearchEnabled = environment.REACT_APP_AI_SEARCH_ENABLED;

  return (
    <div className="split-view-list-header-container">
      <div className="split-view-list-header">
        {/* AI検索が有効な場合のみタブを表示 */}
        {isAISearchEnabled && (
          <div className="split-view-list-header-tab">
            <SplitViewListTabs {...tabProps} />
          </div>
        )}
      </div>
    </div>
  );
};
```

## テスト

### 単体テスト

AI検索が無効の場合のテストケースが追加されています：

```typescript
describe('AI検索が無効の場合', () => {
  beforeEach(() => {
    jest.spyOn(environment, 'REACT_APP_AI_SEARCH_ENABLED', 'get').mockReturnValue(false);
  });

  it('AI検索が無効の場合、コンポーネントが表示されない', () => {
    const { container } = render(<SplitViewListTabs {...props} />);
    const tabContainer = container.querySelector('.split-view-list-tabs-container');
    expect(tabContainer).toBeNull();
  });
});
```

### 動作確認

1. **AI検索有効時の確認**
   - `REACT_APP_AI_SEARCH_ENABLED=true` に設定
   - DefaultタブとAISearchタブが表示されることを確認
   - タブ切り替えが正常に動作することを確認

2. **AI検索無効時の確認**
   - `REACT_APP_AI_SEARCH_ENABLED=false` に設定
   - タブが表示されないことを確認
   - 常にDefaultモードで動作することを確認

## 注意事項

### 1. 環境変数の反映

- 環境変数の変更後は、アプリケーションの再ビルドが必要
- 開発環境では、開発サーバーの再起動が必要

### 2. 既存データへの影響

- AI検索を無効にしても、既存のブックマークや検索履歴には影響しない
- AI検索で作成されたデータは引き続き利用可能

### 3. API呼び出し

- AI検索が無効の場合、OpenAI関連のAPI呼び出しは実行されない
- APIキーやエンドポイント設定は不要

## トラブルシューティング

### 問題: 環境変数を設定してもAI検索が無効にならない

**原因**: 
- 環境変数の設定が正しく反映されていない
- アプリケーションの再ビルドが実行されていない

**解決方法**:
1. 環境変数の設定を確認
2. アプリケーションを再ビルド
3. ブラウザのキャッシュをクリア

### 問題: AI検索を有効にしてもタブが表示されない

**原因**:
- 環境変数の値が文字列として評価されている
- 設定値が `'true'` ではなく `true` になっている

**解決方法**:
```bash
# 正しい設定
REACT_APP_AI_SEARCH_ENABLED=true

# 間違った設定
REACT_APP_AI_SEARCH_ENABLED="true"  # 文字列として評価される可能性
```

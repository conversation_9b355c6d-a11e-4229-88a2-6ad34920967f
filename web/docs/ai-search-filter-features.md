# AI検索フィルター機能 詳細仕様書

## 概要

AI検索機能において実装されたフィルター機能は、検索結果を効率的に絞り込むための3つの主要コンポーネントから構成されています：

1. **SortButton** - ソート機能
2. **FilterDropdown** - 日付フィルター機能  
3. **DataSourceFilterDropdown** - データソースフィルター機能

これらのコンポーネントは連携して動作し、ユーザーが検索結果を柔軟にフィルタリング・ソートできる機能を提供します。

## アーキテクチャ概要

### 状態管理
- **Redux風のReducer**: `splitViewReducer`を使用した状態管理
- **Context**: `IContext`型でソート・フィルター状態を管理
- **非同期処理**: `useAsync`フックによるフィルターオプション数の計算

### 型定義
```typescript
// フィルターオプション
export type FilterOption = IDisplayDateFilterOption | IKindFilterOption;

// 日付フィルター
export interface IDisplayDateFilterOption {
  key: 'displayDate';
  option: number;
  from?: string;
  to?: string;
}

// データソースフィルター
export interface IKindFilterOption {
  key: 'kind';
  option: string[];
}

// ソート設定
export interface ISortOrder {
  key: 'displayDate' | 'score';
  order: 'asc' | 'desc';
  priority?: number;
}
```

## 1. SortButton コンポーネント

### 概要
検索結果のソート機能を提供するボタンコンポーネント。日付またはスコアによる昇順・降順ソートが可能。

### ファイル構成
- **メインファイル**: `web/src/components/commons/atoms/sort-button/SortButton.tsx`
- **スタイル**: `web/src/components/commons/atoms/sort-button/SortButton.scss`
- **ビヘイビア**: `web/src/hooks/behaviors/useSortButtonBehavior.ts`

### Props
```typescript
export interface ISortButtonProps {
  className?: string;
  searchMode?: SearchModeType;  // 'default' | 'Chat'
  listMode?: ListModeType;      // 'initialDisplay' | 'bookmarks' | 'search'
  state?: string;               // 'default' | 'asc' | 'desc'
  onClick?: (e: React.SyntheticEvent<HTMLElement>) => void;
}
```

### 機能詳細

#### ソート状態
```typescript
export const SortButtonState = {
  DEFAULT: 'default',  // ソートなし
  ASC: 'asc',         // 昇順
  DESC: 'desc',       // 降順
};
```

#### 表示ラベルの動的決定
- **ブックマークモード**: 常に「日付」
- **チャットモード**: 「スコア」
- **その他**: 「日付」

#### スタイリング
- ホバー時のブランドカラー適用
- アクティブ状態（asc/desc）でのハイライト表示
- アイコンサイズの調整（デフォルト: 16px、asc/desc: 12px）

### 使用例
```tsx
<SortButton
  searchMode={searchMode}
  listMode={listMode}
  state={state.context.sort.find(condition => condition.key === sortKey)?.order ?? SortButtonState.DEFAULT}
  onClick={onClickSort}
/>
```

## 2. FilterDropdown コンポーネント

### 概要
日付による検索結果フィルタリング機能を提供。期間指定や相対的な日付範囲での絞り込みが可能。

### ファイル構成
- **メインファイル**: `web/src/components/commons/atoms/filter-dropdown/FilterDropdown.tsx`
- **スタイル**: `web/src/components/commons/atoms/filter-dropdown/FilterDropdown.scss`

### Props
```typescript
export interface IFilterDropdownProps {
  className?: string;
  selectedOption: number | undefined;
  selectedKey: 'displayDate';
  state: ISplitViewState;
  listMode?: ListModeType;
  dispatch: SplitViewDispatch;
  reportEvent: EventReporter;
  resultEmptyList?: AsyncState<FilterLeftOptions>;
  dateFilterRef: React.MutableRefObject<{
    from?: string;
    to?: string;
  }>;
  searchMode?: SearchModeType;
}
```

### フィルターオプション
```typescript
export const FilterOptions = {
  displayDate: [
    { header: '期間指定なし' },      // option: 0
    { header: '24時間以内' },       // option: 1
    { header: '1週間以内' },        // option: 2
    { header: '1ヶ月以内' },        // option: 3
    { header: '半年以内' },         // option: 4
    { header: '1年以内' },          // option: 5
    { header: '期間を指定' },       // option: 6 (カスタム範囲)
  ],
};
```

### 機能詳細

#### 結果数表示
- 各フィルターオプションに該当する検索結果数を表示
- 1000件を超える場合は「999+」として表示
- チャットモードでは結果数表示を無効化

#### 期間指定機能
- 「期間を指定」選択時にDateRangeDialogを表示
- カスタム日付範囲の設定が可能
- タイムゾーン対応（Asia/Tokyo）

#### 空結果の処理
- 該当件数が0の場合、`result-empty`クラスを適用
- 視覚的に選択不可であることを示す

### 使用例
```tsx
<FilterDropdown
  selectedKey="displayDate"
  selectedOption={displayDateOption as number}
  state={state}
  listMode={listMode}
  dispatch={dispatch}
  reportEvent={reportEvent}
  resultEmptyList={emptyFilterOptions}
  dateFilterRef={dateFilterRef}
  searchMode={searchMode}
/>
```

## 3. DataSourceFilterDropdown コンポーネント

### 概要
データソース（SPO、Outlook、Teams）による検索結果フィルタリング機能。複数選択が可能。

### ファイル構成
- **メインファイル**: `web/src/components/commons/atoms/datasourcefilter-dropdown/dataSourceFilterDropDown.tsx`
- **スタイル**: `web/src/components/commons/atoms/datasourcefilter-dropdown/dataSourceFilterDropDown.scss`

### Props
```typescript
export interface IFilterDropdownProps {
  selectedKey: 'kind' | 'displayDate';
  className?: string;
  selectedOption: string[] | undefined;
  state: ISplitViewState;
  listMode?: ListModeType;
  dispatch: SplitViewDispatch;
  reportEvent: EventReporter;
  resultEmptyList?: AsyncState<FilterLeftOptions>;
  sourceFilterRef: React.MutableRefObject<string>;
  searchMode?: SearchModeType;
}
```

### データソースオプション
```typescript
export const FilterOptions = [
  { header: 'SPO', variables: { key: 'SPO', index: 0 } },
  { header: 'Outlook', variables: { key: 'Mail', index: 1 } },
  { header: 'Teams', variables: { key: 'Chat', index: 2 } },
];
```

### 機能詳細

#### 複数選択機能
- `multiple`プロパティによる複数データソースの同時選択
- 選択状態の視覚的フィードバック

#### アイコン表示
- `DataSourceIcon`コンポーネントによる各データソースのアイコン表示
- 選択時とドロップダウン内で一貫したアイコン表示

#### 結果数表示
- 各データソースに該当する検索結果数を表示
- 検索結果がある場合のみ数値を表示

### 使用例
```tsx
<DataSourceFilterDropdown
  selectedKey="kind"
  selectedOption={kindOption as string[]}
  listMode={listMode}
  state={state}
  dispatch={dispatch}
  reportEvent={reportEvent}
  resultEmptyList={emptyFilterOptions}
  sourceFilterRef={sourceFilterRef}
  searchMode={searchMode}
/>
```

## 共通機能・ユーティリティ

### useFilterOptionBehavior フック

フィルターオプションの選択処理を管理するカスタムフック。

```typescript
export type UseFilterOptionBehaviorReturn = {
  onSelectOption: (option: FilterOption) => void
}

const useFilterOptionBehavior = (state: ISplitViewState, dispatch: SplitViewDispatch) => {
  function onSelectOption(option: FilterOption) {
    const newFilterOptions = [
      ...(state.context.filter?.filter(({ key }) => key !== option.key) ?? []),
      option,
    ];
    dispatch({
      type: 'SET_FILTER',
      payload: { filter: newFilterOptions },
    });
  }
  return { onSelectOption };
};
```

#### 機能
- 既存のフィルターから同じキーのものを除去
- 新しいフィルターオプションを追加
- `SET_FILTER`アクションをディスパッチ

### フィルター設定 (filterSettings.ts)

#### 定数
```typescript
export const MaxFilterDisplayCount = 1000; // 表示する最大件数
```

#### FilterLeftOptions型
```typescript
export type FilterLeftOptions = {
  displayDate: number[],  // 各日付フィルターオプションの該当件数
  kind: number[],         // 各データソースフィルターオプションの該当件数
}
```

#### getEmptyFilterOptions関数
非同期でフィルターオプションごとの該当件数を計算する関数。

```typescript
export async function getEmptyFilterOptions(
  { list, context }: ISplitViewState,
): Promise<FilterLeftOptions>
```

- 現在のリストとコンテキストから各フィルターオプションの該当件数を計算
- Promise.allを使用した並列処理で高速化
- 結果をFilterLeftOptions型で返却

### SplitViewReducer

#### フィルター関連アクション

**SET_FILTER**
```typescript
type SetFilterAction = { type: 'SET_FILTER', payload: { filter: FilterOption[] } };

function setFilterImpl(state: ISplitViewState, action: SetFilterAction) {
  return {
    ...state,
    activeId: '',  // 選択記事をリセット
    context: {
      ...state.context,
      filter: action.payload.filter,
      timestamp: new Date(),  // 変更タイムスタンプを更新
    },
  };
}
```

**SET_SORT**
```typescript
type SetSortAction = { type: 'SET_SORT', payload: { sort: ISortOrder[] } };

function setSortImpl(state: ISplitViewState, action: SetSortAction) {
  return {
    ...state,
    activeId: '',  // 選択記事をリセット
    context: {
      ...state.context,
      sort: action.payload.sort,
      timestamp: new Date(),
    },
  };
}
```

## スタイリング詳細

### SortButton.scss
```scss
.sort-button {
  .ui-icon {
    display: flex;
    align-items: center;

    &.arrow-default-sort-icon > * {
      width: 16px;
      height: 16px;
      padding-left: 8px;
    }

    &.arrow-ascending-sort-icon,
    &.arrow-descending-sort-icon > * {
      width: 12px;
      height: 12px;
      padding-left: 13px;
    }
  }

  .button-label {
    font-size: 0.75rem;
  }

  &:hover {
    .button-label, .sort-icon {
      color: var(--color-guide-brand-main-foreground);
      font-weight: bold;
    }
  }

  &.is-desc, &.is-asc {
    .button-label, .arrow-descending-sort-icon, .arrow-ascending-sort-icon {
      color: var(--color-guide-brand-main-foreground);
    }
  }
}
```

### FilterDropdown.scss
```scss
.filter-dropdown {
  flex-grow: 1;
  width: 9rem;

  & [role=button] span {
    font-size: 0.75rem;
    font-weight: normal;
  }

  .filter-item {
    &:hover {
      background: var(--color-guide-brand-background-1);
    }

    &.result-empty {
      background-color: var(--color-guide-foreground-1-dark);

      &:hover {
        background-color: var(--color-guide-foreground-1-dark);
      }
    }

    &.selected {
      background-color: var(--color-guide-brand-foreground-active);
    }
  }
}
```

### DataSourceFilterDropdown.scss
```scss
.datasource-filter-dropdown {
  flex-grow: 2;
  width: 15rem;

  .ui-dropdown__container {
    width: 100%;
  }

  .ui-dropdown__selected-items .ui-button__content {
    font-weight: initial;
    font-size: 12px;
  }
}

.filter-item {
  &:hover {
    background: var(--color-guide-brand-background-1) !important;
  }

  &.result-empty {
    &:hover {
      background-color: var(--color-guide-foreground-1-dark) !important;
    }
  }

  .ui-box {
    display: flex;
  }
}
```

## 使用パターン・実装例

### SplitViewList.tsx での統合使用例

```tsx
<div className="filter-controls">
  <SortButton
    searchMode={searchMode}
    listMode={listMode}
    state={state.context.sort.find(condition => {
      return condition.key === sortKey;
    })?.order ?? SortButtonState.DEFAULT}
    onClick={onClickSort}
  />

  <FilterDropdown
    selectedKey="displayDate"
    selectedOption={displayDateOption as number}
    state={state}
    listMode={listMode}
    dispatch={dispatch}
    reportEvent={reportEvent}
    resultEmptyList={emptyFilterOptions}
    dateFilterRef={dateFilterRef}
    searchMode={searchMode}
  />

  <DataSourceFilterDropdown
    selectedKey="kind"
    selectedOption={kindOption as string[]}
    listMode={listMode}
    state={state}
    dispatch={dispatch}
    reportEvent={reportEvent}
    resultEmptyList={emptyFilterOptions}
    sourceFilterRef={sourceFilterRef}
    searchMode={searchMode}
  />
</div>
```

### フィルター状態の初期化

```typescript
// AI検索用の初期状態
export function initSplitViewStateForAI(): ISplitViewState {
  return {
    listView: SplitViewListView.SEARCH_COMPLETED,
    listMessage: SplitViewListMessage.INITIAL_DISPLAY,
    list: [],
    activeId: '',
    detailView: SplitViewDetailView.DEFAULT,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: {
      sort: [],
      filter: [{ key: 'displayDate', option: 6 }], // デフォルトで期間指定フィルター
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
}
```

### ブックマーク一覧遷移時のリセット処理

```typescript
export function onSwitchBookmarksListImpl(dispatch: SplitViewDispatch): void {
  // フィルターをクリア
  dispatch({
    type: 'SET_FILTER',
    payload: { filter: [] },
  });

  // ソートをクリア
  dispatch({
    type: 'SET_SORT',
    payload: { sort: [] },
  });
}
```

## 特殊な動作・注意点

### チャットモードでの動作
- **結果数表示**: チャットモードでは結果数の表示を無効化
- **ソートラベル**: 「スコア」表示に変更
- **空結果スタイル**: `result-empty`クラスを適用しない

### 検索モード変更時の処理
- `sourceFilterRef`の初期化
- フィルター状態のリセット

### 日付フィルターの計算
```typescript
export function calculateResultCount(items: ISplitViewListSingle[], from?: string, to?: string) {
  const fromDateTime = from ? dayjs.tz(from, 'Asia/Tokyo') : dayjs.tz('1970-01-01', 'Asia/Tokyo').startOf('day');
  const toDateTime = (to ? dayjs.tz(to, 'Asia/Tokyo') : dayjs.tz(dayjs(), 'Asia/Tokyo')).add(1, 'day').startOf('day');
  return items.filter((item) => dayjs(item.reposUpdatedDate).isBetween(fromDateTime, toDateTime, undefined, '[]')).length;
}
```

### パフォーマンス最適化
- `React.useMemo`による計算結果のメモ化
- `React.useCallback`によるイベントハンドラーの最適化
- 非同期処理による結果数計算の並列化

## テスト

各コンポーネントには対応するテストファイルが存在：
- `SortButton.test.tsx`
- `FilterDropdown.test.tsx`
- `dataSourceFilterDropDown.test.tsx`
- `useFilterOptionBehavior.test.ts`

テストでは以下の項目をカバー：
- プロパティの正しい表示
- イベントハンドリング
- 状態変更の検証
- エラーケースの処理

## まとめ

AI検索のフィルター機能は、3つの主要コンポーネントが連携して動作する高度なフィルタリングシステムです。Redux風の状態管理、型安全性、パフォーマンス最適化、アクセシビリティを考慮した設計となっており、ユーザーが効率的に検索結果を絞り込むことができる機能を提供しています。

### 主な特徴
1. **モジュラー設計**: 各コンポーネントが独立して動作し、再利用可能
2. **型安全性**: TypeScriptによる厳密な型定義
3. **状態管理**: Redux風のReducerパターンによる一元的な状態管理
4. **パフォーマンス**: メモ化と非同期処理による最適化
5. **ユーザビリティ**: 直感的なUI/UXとアクセシビリティ対応
6. **テスタビリティ**: 包括的なテストカバレッジ

この設計により、拡張性と保守性を兼ね備えた堅牢なフィルター機能を実現しています。

## テスト要件策定のためのチェックポイント

### 1. SortButton コンポーネントのテスト項目

#### 基本動作テスト
- [ ] ソートボタンクリック時に状態が正しく変化する（default → asc → desc → default）
- [ ] 検索モードに応じてラベルが正しく表示される（日付/スコア）
- [ ] リストモードに応じてラベルが正しく表示される（ブックマーク時は常に日付）
- [ ] onClick イベントが正しく発火する
- [ ] 無効状態（disabled）で動作しない

#### UI表示テスト
- [ ] アイコンが状態に応じて正しく表示される（ArrowSort/ArrowUp/ArrowDown）
- [ ] アイコンサイズが状態に応じて調整される（16px/12px）
- [ ] ホバー時にスタイルが変化する
- [ ] アクティブ状態（asc/desc）でハイライト表示される
- [ ] クラス名が状態に応じて正しく設定される（is-asc/is-desc）

#### 状態管理テスト
- [ ] SET_SORT アクションが正しくディスパッチされる
- [ ] activeId がリセットされる
- [ ] timestamp が更新される
- [ ] 複数ソート条件の優先度が正しく処理される

### 2. FilterDropdown コンポーネントのテスト項目

#### 基本動作テスト
- [ ] フィルターオプション選択時に状態が更新される
- [ ] 期間指定オプション選択時にDateRangeDialogが表示される
- [ ] カスタム日付範囲の設定が正しく反映される
- [ ] フィルター変更時にSET_FILTERアクションがディスパッチされる
- [ ] 同じキーの既存フィルターが置き換えられる

#### 結果数表示テスト
- [ ] 各フィルターオプションの該当件数が正しく表示される
- [ ] 1000件超過時に「999+」と表示される
- [ ] チャットモードで結果数表示が無効化される
- [ ] 計算中は「計算中」と表示される
- [ ] 該当件数0の場合にresult-emptyクラスが適用される

#### 日付計算テスト
- [ ] 24時間以内フィルターが正しく動作する
- [ ] 1週間以内フィルターが正しく動作する
- [ ] 1ヶ月以内フィルターが正しく動作する
- [ ] 半年以内フィルターが正しく動作する
- [ ] 1年以内フィルターが正しく動作する
- [ ] カスタム期間フィルターが正しく動作する
- [ ] タイムゾーン（Asia/Tokyo）が正しく適用される

#### UI状態テスト
- [ ] プレースホルダーテキストが正しく表示される
- [ ] 選択状態が視覚的に表現される
- [ ] 空結果時のスタイルが適用される
- [ ] ドロップダウンの開閉が正常動作する

### 3. DataSourceFilterDropdown コンポーネントのテスト項目

#### 基本動作テスト
- [ ] 複数データソースの同時選択が可能
- [ ] データソース選択時に状態が更新される
- [ ] sourceFilterRef が正しく更新される
- [ ] 検索モード変更時にsourceFilterRefがリセットされる
- [ ] SET_FILTERアクションが正しくディスパッチされる

#### データソース処理テスト
- [ ] SPO選択時に正しいキー（'SPO'）が設定される
- [ ] Outlook選択時に正しいキー（'Mail'）が設定される
- [ ] Teams選択時に正しいキー（'Chat'）が設定される
- [ ] 複数選択時にキーが正しく結合される（カンマ区切り）
- [ ] 選択解除時に状態が正しく更新される

#### アイコン表示テスト
- [ ] 各データソースのアイコンが正しく表示される
- [ ] 選択時とドロップダウン内でアイコンが一貫している
- [ ] DataSourceIconコンポーネントが正しく呼び出される

#### 結果数表示テスト
- [ ] 各データソースの該当件数が正しく表示される
- [ ] 検索結果がない場合は数値が表示されない
- [ ] 結果数の計算が正確に行われる

### 4. 統合テスト項目

#### フィルター連携テスト
- [ ] 複数フィルターの同時適用が正常動作する
- [ ] フィルター変更時に他のフィルターに影響しない
- [ ] フィルターとソートの組み合わせが正常動作する
- [ ] フィルター適用後の検索結果が正しく絞り込まれる

#### モード切り替えテスト
- [ ] 検索モード変更時にフィルター動作が適切に変化する
- [ ] チャットモードでの特別な動作が正しく実装される
- [ ] リストモード変更時にフィルターが適切にリセットされる
- [ ] ブックマークモード遷移時のリセット処理が正常動作する

#### 状態同期テスト
- [ ] useFilterOptionBehaviorが正しく動作する
- [ ] 状態変更時にタイムスタンプが更新される
- [ ] activeIdのリセットが正しく行われる
- [ ] コンテキストの同期が正常に保たれる

#### パフォーマンステスト
- [ ] getEmptyFilterOptions関数の並列処理が正常動作する
- [ ] 大量データでのフィルター処理性能が許容範囲内
- [ ] メモ化による最適化が効果的に動作する
- [ ] 不要な再レンダリングが発生しない

### 5. エラーケース・境界値テスト

#### 異常系テスト
- [ ] 不正なフィルターオプション値でエラーが発生しない
- [ ] 存在しないデータソースキーでエラーが発生しない
- [ ] 日付フィルターで不正な日付値を処理できる
- [ ] ネットワークエラー時でもフィルター操作が可能
- [ ] メモリ不足時の動作が安定している

#### 境界値テスト
- [ ] 最大表示件数（1000件）の境界値処理が正確
- [ ] 日付範囲の境界値（1970年、未来日付）が正しく処理される
- [ ] 空文字列や null 値の適切な処理
- [ ] 非常に長い検索結果リストでの動作確認

#### データ整合性テスト
- [ ] フィルター状態とUI表示の一貫性が保たれる
- [ ] 並行操作時の状態競合が適切に処理される
- [ ] ブラウザリロード後の状態復元が正常動作する

### 6. ユーザビリティ・アクセシビリティテスト

#### キーボード操作テスト
- [ ] Tabキーでのフォーカス移動が正常動作する
- [ ] Enterキーでのフィルター選択が可能
- [ ] Escapeキーでのドロップダウン閉じが動作する
- [ ] 矢印キーでのオプション選択が可能

#### スクリーンリーダー対応テスト
- [ ] ARIA属性が適切に設定されている
- [ ] フィルター状態の変化が音声で通知される
- [ ] 結果数の変化が適切に読み上げられる

#### レスポンシブ対応テスト
- [ ] モバイルデバイスでの操作性が良好
- [ ] 画面サイズ変更時のレイアウト調整が正常
- [ ] タッチ操作での使いやすさが確保されている

### 7. ログ・分析テスト

#### イベントログテスト
- [ ] フィルター選択時のログが正しく送信される
- [ ] ソート操作時のログが正しく送信される
- [ ] エラー発生時のログが適切に記録される
- [ ] ユーザー行動の分析データが正確に収集される

#### パフォーマンス分析テスト
- [ ] フィルター処理時間の測定と記録
- [ ] メモリ使用量の監視
- [ ] ネットワーク通信量の最適化確認

### 8. 回帰テスト項目

#### 既存機能への影響テスト
- [ ] フィルター機能追加が既存検索機能に影響しない
- [ ] 他のコンポーネントとの相互作用が正常
- [ ] 全体的なアプリケーション性能に悪影響がない

#### バージョン互換性テスト
- [ ] 過去のフィルター設定データとの互換性
- [ ] APIバージョン変更への対応
- [ ] ブラウザ互換性の確保

## テスト自動化の推奨事項

### 単体テスト（Jest + React Testing Library）
- コンポーネントの基本動作
- Props の正しい処理
- イベントハンドリング
- 状態変更の検証

### 統合テスト（Cypress/Playwright）
- ユーザーフローの端到端テスト
- 複数コンポーネント間の連携
- 実際のユーザー操作シミュレーション

### パフォーマンステスト
- Lighthouse による性能測定
- メモリリーク検出
- レンダリング性能の監視

### アクセシビリティテスト
- axe-core による自動チェック
- キーボードナビゲーションテスト
- スクリーンリーダーテスト

これらのテスト項目を体系的に実装することで、フィルター機能の品質と信頼性を確保し、ユーザーエクスペリエンスの向上を図ることができます。

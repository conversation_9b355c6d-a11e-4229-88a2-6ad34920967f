# AISearch OpenAIController 詳細仕様書

## 概要

OpenAIControllerは、AI検索機能の中核を担うAPIコントローラーです。Azure AI Searchを使用してMail（Outlook）とSPO（SharePoint Online）のデータソースから検索を実行し、セマンティック検索とベクトル検索を組み合わせた高度な検索機能を提供します。

## アーキテクチャ概要

### 主要機能
1. **マルチデータソース検索**: Mail（Outlook）とSPO（SharePoint）の並列検索
2. **セマンティック検索**: Azure AI Searchのセマンティック機能を活用
3. **ベクトル検索**: テキストベクトル化による意味的類似性検索
4. **結果マージ・重複排除**: 複数データソースの結果を統合
5. **セキュリティフィルタリング**: ユーザー・グループベースのアクセス制御

### 技術スタック
- **Azure AI Search**: 検索エンジン
- **Azure OpenAI**: ベクトル化（将来的なLLM統合用）
- **ASP.NET Core**: Web APIフレームワーク
- **Microsoft.Identity.Web**: 認証・認可

## コントローラー詳細

### クラス定義
```csharp
[Route("openai")]
[Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
public class OpenAIController : ControllerBase
```

### 設定項目
```csharp
// Azure AI Search共通設定
private readonly string? _searchServiceName;     // 検索サービス名
private readonly string? _searchApiKey;          // API キー

// Mail検索設定
private readonly string? _mailindexName;                    // Mailインデックス名
private readonly string? _mailsemanticConfigurationName;   // Mailセマンティック設定

// SPO検索設定
private readonly string? _spoindexName;                     // SPOインデックス名
private readonly string? _sposemanticConfigurationName;    // SPOセマンティック設定
```

### 設定ファイル構造
```json
{
  "AISearch": {
    "ServiceName": "srch-attane-dev-proto-002",
    "ApiKey": "***",
    "Mail": [
      {
        "MailIndex": "srch-attane-mail-x2-index",
        "MailSemanticConfiguration": "srch-attane-mail4-semanticconf"
      }
    ],
    "SPO": [
      {
        "SPOIndex": "spo5-index",
        "SPOSemanticConfiguration": "spo5-index-semantic-configuration"
      }
    ]
  }
}
```

## API仕様

### エンドポイント
```
POST /openai/aisearch
```

### リクエストモデル
```csharp
public class Request
{
    /// <summary>入力テキスト</summary>
    public string? Input { get; set; }
    
    /// <summary>ユーザーID</summary>
    public string? UserId { get; set; }
    
    /// <summary>グループID</summary>
    public List<string>? GroupIds { get; set; }
    
    /// <summary>日付フィルターFrom</summary>
    public string? From { get; set; }
    
    /// <summary>日付フィルターTo</summary>
    public string? To { get; set; }
    
    /// <summary>検索対象フィルター</summary>
    public string? SourceFilter { get; set; }
}
```

### レスポンス形式
```json
{
  "MergedResult": [
    {
      "Document": {
        "id": "document-id",
        "chunk": "document content",
        "subject": "document title",
        // その他のフィールド
      },
      "SemanticSearch": {
        "RerankerScore": 0.85
      }
    }
  ]
}
```

## 検索処理フロー

### 1. メイン処理（SearchResults）
```csharp
[HttpPost("aisearch")]
public async Task<IActionResult> SearchResults([FromBody] Request request)
{
    // 1. バリデーション
    // 2. Mail検索実行
    // 3. SPO検索実行
    // 4. 結果マージ・重複排除
    // 5. レスポンス返却
}
```

### 2. バリデーション処理
```csharp
// 必須パラメータチェック
if (request == null) return BadRequest("Request cannot be null.");
if (string.IsNullOrEmpty(request.Input)) return BadRequest("Input cannot be empty.");
if (string.IsNullOrEmpty(request.UserId)) return BadRequest("UserId cannot be empty.");
if (request.GroupIds == null || request.GroupIds.Count == 0) 
    return BadRequest("GroupIds cannot be null or empty.");
```

### 3. 並列検索実行
```csharp
// Mail検索
List<SearchResult<SearchDocument>> MailSearchResult = new();
if (!string.IsNullOrEmpty(_mailindexName) && !string.IsNullOrEmpty(_mailsemanticConfigurationName))
{
    MailSearchResult = await MailSearch(request.Input, request.UserId, request.From, request.To, request.SourceFilter);
}

// SPO検索
List<SearchResult<SearchDocument>> SPOSearchResult = new();
if (!string.IsNullOrEmpty(_spoindexName) && !string.IsNullOrEmpty(_sposemanticConfigurationName))
{
    SPOSearchResult = await SPOSearch(request.Input, request.GroupIds, request.From, request.To, request.SourceFilter);
}
```

### 4. 結果マージ・重複排除
```csharp
var MergedResult = allResults
    // IDでグループ化
    .GroupBy(r => r.Document["id"].ToString())
    // スコアが高い方を選択
    .Select(g => g.OrderByDescending(r => r.SemanticSearch.RerankerScore).First())
    // スコア順でソート
    .OrderByDescending(r => r.SemanticSearch.RerankerScore)
    // 上位50件を取得
    .Take(50)
    .ToList();
```

## データソース別検索実装

### Mail検索（MailSearch）

#### セキュリティフィルター
```csharp
var filterClauses = new List<string>
{
    $"security_user_id/any(id: id eq '{userId}')"  // ユーザーベースフィルタリング
};
```

#### 日付フィルター
```csharp
if (!string.IsNullOrEmpty(from))
{
    var fromDate = DateTime.Parse(from).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
    filterClauses.Add($"(receivedDateTime2 ge '{fromDate}')");
}
if (!string.IsNullOrEmpty(to))
{
    var toDate = DateTime.Parse(to).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
    filterClauses.Add($"(receivedDateTime2 le '{toDate}')");
}
```

#### 検索オプション
```csharp
var options = new SearchOptions
{
    SearchFields = { "subject", "chunk" },  // 検索対象フィールド
    VectorSearch = new()
    {
        Queries = {
            new VectorizableTextQuery(query)
            {
                Fields = { "text_vector" },  // ベクトル検索フィールド
            }
        }
    },
    Filter = combinedFilter,
    QueryType = SearchQueryType.Semantic,  // セマンティック検索
    SemanticSearch = new()
    {
        SemanticConfigurationName = _mailsemanticConfigurationName,
    },
};
```

### SPO検索（SPOSearch）

#### グループベースセキュリティフィルター
```csharp
if (groupIds.Count == 1)
{
    securityFilter = $"security_user_id/any(id: id eq '{groupIds[0]}')";
}
else // 複数グループ
{
    var orFilter = string.Join(" or ", groupIds.Select(id => $"id eq '{id}'"));
    securityFilter = $"security_user_id/any(id: {orFilter})";
}
```

#### SPO固有の日付フィルター
```csharp
if (!string.IsNullOrEmpty(from))
{
    var fromDate = DateTime.Parse(from).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
    filterClauses.Add($"(properties/updatedDate ge '{fromDate}')");
}
```

#### 検索オプション
```csharp
var options = new SearchOptions
{
    SearchFields = { "chunk" },  // 本文のみ検索
    VectorSearch = new()
    {
        Queries = {
            new VectorizableTextQuery(query) {
                Fields = { "text_vector" },
            }
        }
    },
    Filter = combinedFilter,
    QueryType = SearchQueryType.Semantic,
    SemanticSearch = new()
    {
        SemanticConfigurationName = _sposemanticConfigurationName,
    },
    Size = 50,  // 最大50件
};
```

## エラーハンドリング

### 階層化されたエラー処理
```csharp
try
{
    // Mail検索
    try
    {
        MailSearchResult = await MailSearch(...);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Mail search error occurred for user {UserId}", request.UserId);
        return StatusCode(500, new { error = $"Mail search error: {ex.Message}" });
    }
    
    // SPO検索
    try
    {
        SPOSearchResult = await SPOSearch(...);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "SPO search error occurred for groups {GroupIds}", string.Join(", ", request.GroupIds));
        return StatusCode(500, new { error = $"SPO search error: {ex.Message}" });
    }
}
catch (Exception ex)
{
    _logger.LogError(ex, "Unexpected error in search request for user {UserId}", request?.UserId);
    return StatusCode(500, new { error = $"Unexpected error: {ex.Message}" });
}
```

### Azure AI Search固有のエラー処理
```csharp
catch (RequestFailedException ex)
{
    _logger.LogError(ex, "[EmbeddingAPIError] userId: {UserId} | query: {Query} | Status: {Status} | ErrorCode: {ErrorCode} | Message: {Message}",
        userId, query, ex.Status, ex.ErrorCode, ex.Message);
    throw;
}
```

## ログ出力

### パフォーマンス監視
```csharp
var startTime = DateTime.UtcNow;
_logger.LogInformation("Search request started for user {UserId}", request?.UserId);

// 処理実行

var duration = DateTime.UtcNow - startTime;
_logger.LogInformation("Search request completed in {Duration}ms for user {UserId}", 
    duration.TotalMilliseconds, request.UserId);
```

### 詳細ログ
```csharp
_logger.LogTrace("Mail search started for user {UserId}", userId);
_logger.LogTrace("Mail search filter conditions: {Filter}", combinedFilter);
_logger.LogTrace("Mail search completed with {Count} results", MailSearchResult.Count);
```

## セキュリティ実装

### 認証・認可
- **Microsoft Identity Platform**: Azure ADベースの認証
- **Bearer Token**: JWTトークンによる認証
- **スコープベース認可**: 適切なスコープの確認

### データアクセス制御
- **ユーザーベースフィルタリング**: Mail検索でのユーザーID制限
- **グループベースフィルタリング**: SPO検索でのグループID制限
- **セキュリティトリミング**: Azure AI Searchレベルでのアクセス制御

## パフォーマンス最適化

### 並列処理
```csharp
// Mail検索とSPO検索の並列実行
var mailTask = MailSearch(request.Input, request.UserId, request.From, request.To, request.SourceFilter);
var spoTask = SPOSearch(request.Input, request.GroupIds, request.From, request.To, request.SourceFilter);

var results = await Task.WhenAll(mailTask, spoTask);
```

### 結果制限
- **Mail検索**: デフォルト制限（Azure AI Searchの設定による）
- **SPO検索**: 最大50件
- **最終結果**: 上位50件（RerankerScoreベース）

### キャッシュ戦略
- **設定値キャッシュ**: コンストラクタでの設定値取得
- **接続プール**: Azure AI Search Clientの再利用

## 将来的な拡張（コメントアウト部分）

### OpenAI統合（現在は無効化）
```csharp
// 将来的なOpenAI統合用のコード（現在はコメントアウト）
// var openAIClient = new AzureOpenAIClient(new Uri(_endpoint), new AzureKeyCredential(_apiKey));
// var chatClient = openAIClient.GetChatClient(_deploymentName);
```

### Teams検索（現在は無効化）
```csharp
// Teams検索機能（現在はコメントアウト）
// private async Task<List<SearchResult<SearchDocument>>> ChatSearch(...)
```

## テスト要件策定のためのチェックポイント

### 1. API基本動作テスト

#### リクエスト・レスポンステスト
- [ ] 正常なリクエストで200 OKが返される
- [ ] 適切なJSON形式のレスポンスが返される
- [ ] MergedResultフィールドが存在する
- [ ] 検索結果がRerankerScoreで降順ソートされている
- [ ] 最大50件の結果制限が適用される

#### バリデーションテスト
- [ ] request=nullで400 BadRequestが返される
- [ ] Input=nullまたは空文字で400 BadRequestが返される
- [ ] UserId=nullまたは空文字で400 BadRequestが返される
- [ ] GroupIds=nullまたは空配列で400 BadRequestが返される
- [ ] エラーメッセージが適切に設定される

#### パラメータテスト
- [ ] 有効なInputで検索が実行される
- [ ] UserIdがMail検索のフィルターに適用される
- [ ] GroupIdsがSPO検索のフィルターに適用される
- [ ] From/To日付フィルターが正しく適用される
- [ ] SourceFilterが正しく適用される

### 2. データソース別検索テスト

#### Mail検索テスト
- [ ] MailSearchメソッドが正しく呼び出される
- [ ] ユーザーIDベースのセキュリティフィルターが適用される
- [ ] 日付フィルター（receivedDateTime2）が正しく適用される
- [ ] 検索フィールド（subject, chunk）が設定される
- [ ] ベクトル検索（text_vector）が実行される
- [ ] セマンティック検索が有効化される
- [ ] Mail検索結果が正しく返される

#### SPO検索テスト
- [ ] SPOSearchメソッドが正しく呼び出される
- [ ] 単一グループIDのセキュリティフィルターが適用される
- [ ] 複数グループIDのORフィルターが適用される
- [ ] 日付フィルター（properties/updatedDate）が正しく適用される
- [ ] 検索フィールド（chunk）が設定される
- [ ] Size=50の制限が適用される
- [ ] SPO検索結果が正しく返される

#### 結果マージテスト
- [ ] Mail検索とSPO検索の結果が正しくマージされる
- [ ] 重複IDの結果が適切に排除される
- [ ] RerankerScoreが高い結果が優先される
- [ ] 最終結果が上位50件に制限される
- [ ] 空の検索結果が適切に処理される

### 3. エラーハンドリングテスト

#### 設定エラーテスト
- [ ] 検索サービス名が未設定時のエラー処理
- [ ] APIキーが未設定時のエラー処理
- [ ] インデックス名が未設定時のスキップ処理
- [ ] セマンティック設定が未設定時のスキップ処理

#### Azure AI Searchエラーテスト
- [ ] 認証エラー（401）の適切な処理
- [ ] 権限エラー（403）の適切な処理
- [ ] インデックス不存在エラー（404）の適切な処理
- [ ] クエリ構文エラー（400）の適切な処理
- [ ] サービス利用不可エラー（503）の適切な処理
- [ ] RequestFailedExceptionの詳細ログ出力

#### 一般的なエラーテスト
- [ ] ネットワークタイムアウトエラーの処理
- [ ] メモリ不足エラーの処理
- [ ] 予期しない例外の500エラー返却
- [ ] エラー時の適切なログ出力

### 4. セキュリティテスト

#### 認証テスト
- [ ] Bearer tokenなしでUnauthorized（401）が返される
- [ ] 無効なtokenでUnauthorized（401）が返される
- [ ] 期限切れtokenでUnauthorized（401）が返される
- [ ] 適切なスコープを持つtokenで認証成功

#### 認可テスト
- [ ] 他ユーザーのUserIdでアクセス拒否
- [ ] 権限のないGroupIdでアクセス拒否
- [ ] セキュリティフィルターの適切な適用
- [ ] データアクセス制御の確認

#### データ保護テスト
- [ ] ユーザーがアクセス権のないデータが結果に含まれない
- [ ] グループがアクセス権のないデータが結果に含まれない
- [ ] セキュリティトリミングの正常動作
- [ ] 機密情報の適切なマスキング

### 5. パフォーマンステスト

#### レスポンス時間テスト
- [ ] 通常の検索リクエストが5秒以内に完了
- [ ] 大量データ検索が10秒以内に完了
- [ ] 並列検索の効果的な動作
- [ ] タイムアウト設定の適切な動作

#### スループットテスト
- [ ] 同時リクエスト処理能力の確認
- [ ] 負荷増加時の性能劣化の測定
- [ ] リソース使用量の監視
- [ ] メモリリークの検出

#### スケーラビリティテスト
- [ ] 検索結果数増加時の性能影響
- [ ] ユーザー数増加時の性能影響
- [ ] データ量増加時の性能影響

### 6. ログ・監視テスト

#### ログ出力テスト
- [ ] 検索開始ログの出力確認
- [ ] 検索完了ログの出力確認
- [ ] 実行時間ログの出力確認
- [ ] エラーログの詳細情報確認
- [ ] トレースログの適切な出力

#### 監視・メトリクステスト
- [ ] Application Insightsへのメトリクス送信
- [ ] パフォーマンスカウンターの記録
- [ ] エラー率の監視
- [ ] 可用性の監視

### 7. 統合テスト

#### エンドツーエンドテスト
- [ ] フロントエンドからAPIまでの完全なフロー
- [ ] 実際のAzure AI Searchサービスとの統合
- [ ] 実際のデータでの検索結果確認
- [ ] ユーザーシナリオベースのテスト

#### 外部サービス統合テスト
- [ ] Azure AI Searchサービスとの接続確認
- [ ] Microsoft Identity Platformとの認証確認
- [ ] Application Insightsとの連携確認

### 8. 回帰テスト

#### 機能回帰テスト
- [ ] 既存の検索機能への影響確認
- [ ] 他のAPIエンドポイントへの影響確認
- [ ] 設定変更時の動作確認

#### パフォーマンス回帰テスト
- [ ] 新機能追加後の性能劣化確認
- [ ] メモリ使用量の変化確認
- [ ] レスポンス時間の変化確認

## テスト自動化の推奨事項

### 単体テスト（xUnit + Moq）
```csharp
[Fact]
public async Task SearchResults_ValidRequest_ReturnsOkResult()
{
    // Arrange
    var mockLogger = new Mock<ILogger<OpenAIController>>();
    var mockConfiguration = new Mock<IConfiguration>();
    // Setup mocks...

    var controller = new OpenAIController(mockConfiguration.Object, mockLogger.Object);
    var request = new OpenAIController.Request { /* valid data */ };

    // Act
    var result = await controller.SearchResults(request);

    // Assert
    Assert.IsType<OkObjectResult>(result);
}
```

### 統合テスト（ASP.NET Core Test Host）
```csharp
[Fact]
public async Task SearchResults_IntegrationTest()
{
    // Arrange
    var client = _factory.CreateClient();
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", validToken);

    var request = new { /* test data */ };
    var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

    // Act
    var response = await client.PostAsync("/openai/aisearch", content);

    // Assert
    response.EnsureSuccessStatusCode();
    var responseContent = await response.Content.ReadAsStringAsync();
    // Verify response content...
}
```

### パフォーマンステスト（NBomber）
```csharp
var scenario = Scenario.Create("search_scenario", async context =>
{
    var request = new { /* test data */ };
    var response = await httpClient.PostAsync("/openai/aisearch", content);
    return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
})
.WithLoadSimulations(
    Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(5))
);
```

これらのテスト項目を体系的に実装することで、OpenAIControllerの品質と信頼性を確保し、AI検索機能の安定した運用を実現できます。

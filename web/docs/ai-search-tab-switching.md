# AI検索タブ切り替え機能 詳細仕様書

## 概要

AI検索機能におけるタブ切り替えは、**Default**モードと**AISearch**（Chat）モードを切り替える重要な機能です。タブ切り替えによって検索方式、UI表示、フィルター動作、状態管理が大きく変化し、ユーザーエクスペリエンスに直接影響を与えます。

## タブの種類と定義

### SearchModeType
```typescript
export const SearchListMode = {
  DEFAULT: 'default',    // 従来の検索モード
  Chat: 'Chat',         // AI検索（チャット）モード
};

export type SearchModeType = ValueOf<typeof SearchListMode>;
```

### タブ表示
- **Default**: 従来の検索機能
- **AISearch**: AI検索（自然言語検索）機能

## アーキテクチャ概要

### 状態管理
- **SearchMode**: `useState<SearchModeType>`による現在のタブ状態管理
- **ListMode**: 検索結果表示モード（SEARCH/BOOKMARKS/INITIAL_DISPLAY）
- **SplitViewState**: 検索結果、フィルター、ソート状態の管理

### 主要コンポーネント
1. **SplitViewListTabs**: タブUI本体
2. **SplitViewList**: タブ切り替えロジックとUI制御
3. **SearchInput**: 検索入力フィールド（モード別プレースホルダー）

## タブ切り替え処理の詳細

### 1. タブクリック時の処理フロー

#### OnClickTabSwitch関数
```typescript
const OnClickTabSwitch = React.useCallback((toBe: boolean) => {
  if (!setSearchMode || !onSwitchBookmarksList) return;
  
  // 1. フィルター・ソート状態をリセット
  onSwitchBookmarksList();
  
  // 2. 現在のモードを確認
  const currentMode = searchMode ?? SearchListMode.DEFAULT;
  const targetMode = toBe ? SearchListMode.Chat : SearchListMode.DEFAULT;
  
  // 3. 同じモードの場合は何もしない（重複クリック防止）
  if (currentMode === targetMode) return;
  
  // 4. モード切り替えを実行
  setSearchMode(targetMode);
}, [setSearchMode, onSwitchBookmarksList, searchMode]);
```

### 2. リセット処理の詳細

#### onSwitchBookmarksList実装
```typescript
export function onSwitchBookmarksListImpl(dispatch: SplitViewDispatch): void {
  // フィルターをクリア
  dispatch({
    type: 'SET_FILTER',
    payload: { filter: [] },
  });

  // ソートをクリア
  dispatch({
    type: 'SET_SORT',
    payload: { sort: [] },
  });
}
```

#### リセットされる状態
- **フィルター設定**: 日付フィルター、データソースフィルターが初期化
- **ソート設定**: ソート条件が初期化
- **選択記事**: `activeId`がクリア
- **タイムスタンプ**: 状態変更時刻が更新

### 3. ログ送信機能

#### sendModeSwitchLog関数
```typescript
function sendModeSwitchLog(
  fromMode: string,
  toMode: string,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'SWITCH_SEARCH_MODE',
    customProperties: {
      fromMode,
      toMode,
      executionDate: new Date().toISOString(),
    },
  });
}
```

## UI・レイアウト変更

### 1. フィルター表示位置の変更

#### チャットモード時の特別なレイアウト
```tsx
{/* チャット検索モードのときはフィルター類を上に */}
{isChatSearchMode && (
  (isBookmarksListMode || isSearchCompleted)
  && (
    <div className="split-view-list-filter-container">
      <SortButton />
      <FilterDropdown />
      <DataSourceFilterDropdown />
    </div>
  )
)}

{/* 通常モード時のフィルター表示 */}
{!isChatSearchMode && (
  (isBookmarksListMode || isSearchCompleted)
  && (
    <div className="split-view-list-filter-container">
      <SortButton />
      <FilterDropdown />
      <DataSourceFilterDropdown />
    </div>
  )
)}
```

### 2. 検索入力フィールドの変更

#### プレースホルダーテキストの動的変更
```typescript
// 通常モード
export const PlaceholderLabel = {
  BLANK: 'キーワードを入力',
  FOCUS: '',
};

// AIモード
export const PlaceholderLabelForAI = {
  BLANK: '自然言語で検索できます',
  FOCUS: '',
};

const placeholder = React.useMemo(() => {
  if (searchMode === SearchListMode.DEFAULT) {
    return view === SearchInputView.NOT_FOCUS_NO_VALUE
      ? PlaceholderLabel.BLANK
      : PlaceholderLabel.FOCUS;
  }

  return view === SearchInputView.NOT_FOCUS_NO_VALUE
    ? PlaceholderLabelForAI.BLANK
    : PlaceholderLabelForAI.FOCUS;
}, [view, searchMode]);
```

### 3. ソートボタンラベルの変更

#### モード別ラベル表示
```typescript
const buttonLabel = React.useMemo(() => {
  if (listMode === ListMode.BOOKMARKS) {
    return '日付';
  }
  return searchMode === 'Chat' ? 'スコア' : '日付';
}, [searchMode, listMode]);
```

## タブ無効化条件

### disabled状態の制御
```typescript
<SplitViewListTabs
  mode={searchMode ?? SearchListMode.DEFAULT}
  isActive={isChatSearchMode}
  onClick={OnClickTabSwitch}
  disabled={isLoading}  // 検索中は無効化
  reportEvent={reportEvent}
/>
```

#### 無効化される条件
- **検索実行中**: `isLoading === true`
- **API通信中**: 検索リクエスト処理中
- **エラー状態**: 一部のエラー状態（設定により変更可能）

## スタイリング・アニメーション

### CSS設計
```scss
.split-view-list-tabs-content {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: flex-end;
  height: 25px;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  a.ui-menu__item {
    color: var(--color-guide-foreground-2);
    cursor: pointer;
    transition: border-color 0.2s, color 0.2s, box-shadow 0.2s, background-color 0.2s;
    border-radius: 4px 4px 0 0;
    padding: 6px 12px 8px;
    
    /* アクティブタブ */
    &[aria-selected='true'] {
      font-weight: bold;
      color: var(--color-guide-brand-main-foreground);
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.15);
      background-color: var(--color-guide-background);
      z-index: 1;
      border-bottom: none;
    }

    /* ホバー効果 */
    &[aria-selected='false']:hover {
      color: var(--color-guide-default-foreground);
      box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.12);
      background-color: rgba(0, 0, 0, 0.03);
    }
  }
}
```

### トランジション効果
- **色変更**: 0.2秒のスムーズな色変化
- **影効果**: ボックスシャドウの段階的変化
- **背景色**: 透明度を使った自然な変化

## モード別の動作差異

### Defaultモード
- **検索方式**: キーワード検索
- **フィルター**: 全機能有効
- **ソート**: 日付ソート
- **結果数表示**: 有効
- **プレースホルダー**: "キーワードを入力"

### AISearch（Chat）モード
- **検索方式**: 自然言語検索
- **フィルター**: 結果数表示無効化
- **ソート**: スコアソート
- **結果数表示**: 無効
- **プレースホルダー**: "自然言語で検索できます"
- **特別なUI**: フィルター位置変更

## エラーハンドリング

### タブ切り替え時のエラー処理
```typescript
// タブクリック時の安全性チェック
const handleDefaultTabClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
  if (disabled || !onClick) return;  // 無効状態またはコールバック未定義時は処理しない
  
  if (isActive) {
    onClick(false, e);
    sendModeSwitchLog('Chat', 'Default', reportEvent);
  }
}, [disabled, onClick, isActive, reportEvent]);
```

### 状態不整合の防止
- **重複クリック防止**: 同じモードのタブクリック時は処理をスキップ
- **コールバック存在チェック**: 必要な関数が未定義の場合は早期リターン
- **状態同期**: タブ表示と内部状態の一貫性保証

## 詳細な状態変化マトリックス

### タブ切り替え時の状態変化表

| 項目 | Default → AISearch | AISearch → Default | 同一タブクリック |
|------|-------------------|-------------------|-----------------|
| **SearchMode** | `DEFAULT` → `Chat` | `Chat` → `DEFAULT` | 変化なし |
| **フィルター状態** | リセット（空配列） | リセット（空配列） | 変化なし |
| **ソート状態** | リセット（空配列） | リセット（空配列） | 変化なし |
| **activeId** | クリア（空文字） | クリア（空文字） | 変化なし |
| **timestamp** | 更新 | 更新 | 変化なし |
| **検索入力値** | 保持 | 保持 | 変化なし |
| **プレースホルダー** | "自然言語で検索できます" | "キーワードを入力" | 変化なし |
| **ソートラベル** | "スコア" | "日付" | 変化なし |
| **フィルター表示位置** | 上部に移動 | 通常位置 | 変化なし |
| **結果数表示** | 無効化 | 有効化 | 変化なし |
| **ログ送信** | 実行 | 実行 | なし |

### リセット処理の詳細

#### 1. フィルター状態のリセット
```typescript
// リセット前の状態例
context: {
  filter: [
    { key: 'displayDate', option: 2 },  // 1週間以内
    { key: 'kind', option: ['SPO', 'Mail'] }  // SPOとOutlook
  ]
}

// リセット後の状態
context: {
  filter: []  // 空配列
}
```

#### 2. ソート状態のリセット
```typescript
// リセット前の状態例
context: {
  sort: [
    { key: 'displayDate', order: 'desc', priority: 1 }
  ]
}

// リセット後の状態
context: {
  sort: []  // 空配列
}
```

#### 3. UI状態の変化
```typescript
// activeIdのリセット
state: {
  activeId: 'item-123'  // 選択されていた記事ID
}
↓
state: {
  activeId: ''  // 空文字（選択解除）
}
```

## 特殊なケースと例外処理

### 1. 検索実行中のタブ切り替え
- **制約**: `disabled={isLoading}`により物理的に無効化
- **UI表示**: タブが半透明表示（opacity: 0.6）
- **クリック**: `pointer-events: none`により無効

### 2. エラー状態でのタブ切り替え
- **基本動作**: エラー状態でもタブ切り替えは可能
- **状態リセット**: エラー状態もリセット処理の対象
- **ログ**: エラー状態からの切り替えもログに記録

### 3. ブックマークモードでのタブ切り替え
```typescript
// ブックマークモード時の特別処理
const onClickBookmarkSwitch = React.useCallback((toBe: boolean) => {
  if (toBe) {
    onSwitchBookmarksList();  // フィルター・ソートリセット
    setReply('');  // チャット返信内容クリア
    setTimeout(() => {
      update();  // UI強制更新（暫定対応）
    }, 500);
  }
}, [onSwitchListMode, onSwitchBookmarksList, setReply, update]);
```

### 4. 初期化処理のタイミング

#### searchModeが変更された時の処理
```typescript
// DataSourceFilterDropdownでの処理例
React.useEffect(() => {
  sourceFilterRef.current = '';  // データソースフィルター参照をクリア
}, [searchMode, sourceFilterRef]);
```

## パフォーマンス考慮事項

### 1. メモ化による最適化
```typescript
// モード判定のメモ化
const isChatSearchMode = React.useMemo(() =>
  searchMode === SearchListMode.Chat, [searchMode]
);

// プレースホルダーテキストのメモ化
const placeholder = React.useMemo(() => {
  // 計算ロジック
}, [view, searchMode]);
```

### 2. 不要な再レンダリング防止
- **useCallback**: イベントハンドラーの最適化
- **useMemo**: 計算結果のキャッシュ
- **React.memo**: コンポーネントレベルの最適化

### 3. 状態更新の最適化
```typescript
// バッチ更新による最適化
dispatch({
  type: 'SET_DATA',
  payload: {
    detailView: SplitViewDetailView.ERROR,
    detailMessage: SplitViewDetailMessage.NOT_SELECTED,
    detail: undefined,
    context: {
      sort: [],
      filter: [],
      timestamp: new Date(),
    },
  },
});
```

## テスト要件策定のためのチェックポイント

### 1. 機能テスト項目

#### 基本動作テスト
- [ ] Defaultタブクリック時にDefaultモードに切り替わる
- [ ] AISearchタブクリック時にChatモードに切り替わる
- [ ] 同一タブの重複クリックで状態が変化しない
- [ ] タブ切り替え時にフィルター状態がリセットされる
- [ ] タブ切り替え時にソート状態がリセットされる
- [ ] タブ切り替え時にactiveIdがクリアされる

#### UI表示テスト
- [ ] アクティブタブが正しくハイライト表示される
- [ ] プレースホルダーテキストがモードに応じて変化する
- [ ] ソートボタンのラベルがモードに応じて変化する
- [ ] チャットモード時にフィルターが上部に表示される
- [ ] 結果数表示がチャットモードで無効化される

#### 無効化テスト
- [ ] 検索実行中にタブが無効化される
- [ ] 無効化時にクリックイベントが発火しない
- [ ] 無効化時に視覚的フィードバックが表示される

### 2. 統合テスト項目

#### 状態管理テスト
- [ ] タブ切り替え後の検索実行が正常動作する
- [ ] フィルター適用後のタブ切り替えで状態がリセットされる
- [ ] ソート適用後のタブ切り替えで状態がリセットされる
- [ ] ブックマークモードでのタブ切り替えが正常動作する

#### ログ送信テスト
- [ ] Default→AISearchでログが送信される
- [ ] AISearch→Defaultでログが送信される
- [ ] ログに正しいfromMode/toModeが含まれる
- [ ] 実行日時が正しく記録される

### 3. エラーケーステスト

#### 異常系テスト
- [ ] コールバック関数未定義時にエラーが発生しない
- [ ] 不正なモード値でもクラッシュしない
- [ ] ネットワークエラー時でもタブ切り替えが可能
- [ ] メモリ不足時の動作確認

#### 境界値テスト
- [ ] 大量データ表示中のタブ切り替え
- [ ] 高頻度でのタブ切り替え操作
- [ ] 長時間使用後のタブ切り替え

### 4. パフォーマンステスト

#### レスポンス性能
- [ ] タブ切り替えの応答時間（目標: 100ms以下）
- [ ] 大量データ時の切り替え性能
- [ ] メモリ使用量の変化測定

#### UI応答性
- [ ] アニメーション効果の滑らかさ
- [ ] 連続クリック時の応答性
- [ ] 他の操作との並行実行性能

### 5. アクセシビリティテスト

#### キーボード操作
- [ ] Tabキーでのフォーカス移動
- [ ] Enterキーでのタブ切り替え
- [ ] スクリーンリーダー対応

#### ARIA属性
- [ ] aria-selected属性の正しい設定
- [ ] role属性の適切な使用
- [ ] アクセシブルな名前の提供

## まとめ

AI検索のタブ切り替え機能は、単純なUI切り替えを超えて、検索方式、状態管理、UI表示、パフォーマンスに広範囲な影響を与える重要な機能です。テスト設計時は、基本的な切り替え動作だけでなく、状態リセット、UI変化、エラーハンドリング、パフォーマンス、アクセシビリティまで包括的にカバーする必要があります。

特に重要なのは、タブ切り替え時の**状態リセット処理**と**UI表示の一貫性**であり、これらが正しく動作することでユーザーエクスペリエンスの品質が保たれます。
